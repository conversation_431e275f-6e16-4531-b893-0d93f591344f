#!/usr/bin/env python3
"""
MEXC Futures Client - Optimized for Futures Trading Only

This is a streamlined version of the MEXC client that focuses exclusively
on futures trading operations. All spot trading functionality has been
removed for better performance and maintainability.

Author: Trading Bot System
Version: 2.0 (Futures Only)
"""

import aiohttp
import asyncio
import hashlib
import hmac
import json
import logging
import random
import time
from typing import Dict, Optional, List
import yaml

class MEXCFuturesClient:
    """
    Optimized MEXC Futures Trading Client
    
    Features:
    - Futures positions management
    - Futures account balance
    - Futures open orders
    - Price data retrieval
    - Streamlined API calls
    """
    
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        """Load trading configuration from config.ymal"""
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
        except Exception as e:
            logging.warning(f"Could not load MEXC config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _get_headers(self, signed: bool = False) -> Dict[str, str]:
        """Get request headers"""
        headers = {'Content-Type': 'application/json'}
        if signed and self.api_key:
            headers['ApiKey'] = self.api_key
        return headers

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        if not self.api_secret:
            return ""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _generate_futures_signature(self, sign_params: str = None) -> str:
        """Generate futures API signature - compatible with original format"""
        if not self.api_secret:
            return ""
        timestamp = int(time.time() * 1000)
        if sign_params:
            sign = f"{self.api_key}{timestamp}{sign_params}"
        else:
            sign = f"{self.api_key}{timestamp}"
        return hmac.new(
            self.api_secret.encode('utf-8'),
            sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _get_futures_headers(self) -> dict:
        """Get futures API headers"""
        timestamp = int(time.time() * 1000)
        return {
            "ApiKey": self.api_key,
            "Request-Time": str(timestamp),
            "Content-Type": "application/json"
        }

    async def get_price_data(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        """Get price data from MEXC with random delay"""
        try:
            await asyncio.sleep(random.uniform(1, 3))

            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_price_data(data, symbol)
                else:
                    logging.error(f"Error getting price data for {symbol}: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_price_data for {symbol}: {e}")
            return None

    def _format_price_data(self, ticker_data: Dict, symbol: str = "PAXGUSDT") -> Dict:
        """Format price data for display - compatible with original format"""
        def safe_float(value, default=0.0):
            try:
                return float(value) if value else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))
        price_change_24h = safe_float(ticker_data.get("priceChange"))
        open_price_24h = safe_float(ticker_data.get("openPrice"))

        # Calculate day open price (use 24h open as fallback)
        day_open_price = open_price_24h
        day_price_change = current_price - day_open_price
        day_price_change_percent = (day_price_change / day_open_price * 100) if day_open_price > 0 else 0.0

        return {
            "symbol": ticker_data.get("symbol", symbol),
            "current_price": current_price,
            "day_open_price": day_open_price,
            "day_price_change": day_price_change,
            "day_price_change_percent": day_price_change_percent,
            "price_change_24h": price_change_24h,
            "price_change_percent_24h": safe_float(ticker_data.get("priceChangePercent")),
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume")),
            "quote_volume": safe_float(ticker_data.get("quoteVolume"))
        }

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        """
        Get futures positions
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            List of position data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            await asyncio.sleep(random.uniform(1, 3))

            url = f"{self.futures_base_url}/api/v1/private/position/open_positions"

            data_original = {}
            if symbol:
                data_original["symbol"] = symbol

            # Create query string for signature
            query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
            signature = self._generate_futures_signature(query_string if query_string else None)

            # Build headers
            headers = self._get_futures_headers()
            headers["Signature"] = signature

            # Build full URL with params
            if query_string:
                url = f"{url}?{query_string}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures positions: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_positions: {e}")
            return []

    async def get_futures_account_assets(self) -> Optional[List]:
        """
        Get futures account balance
        
        Returns:
            List of account asset data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            await asyncio.sleep(random.uniform(1, 3))

            url = f"{self.futures_base_url}/api/v1/private/account/assets"

            # No query parameters needed for this endpoint
            signature = self._generate_futures_signature()

            # Build headers
            headers = self._get_futures_headers()
            headers["Signature"] = signature

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures account assets: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_account_assets: {e}")
            return []

    async def get_futures_open_orders(self, symbol: str = None) -> Optional[List]:
        """
        Get futures open orders
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            List of open order data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            await asyncio.sleep(random.uniform(1, 3))

            url = f"{self.futures_base_url}/api/v1/private/order/list/open_orders"

            data_original = {}
            if symbol:
                data_original["symbol"] = symbol

            query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
            signature = self._generate_futures_signature(query_string if query_string else None)

            headers = self._get_futures_headers()
            headers["Signature"] = signature

            if query_string:
                url = f"{url}?{query_string}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures open orders: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_open_orders: {e}")
            return []

# Alias for backward compatibility
MEXCClient = MEXCFuturesClient
