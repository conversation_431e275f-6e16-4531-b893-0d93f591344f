#!/usr/bin/env python3
"""
MEXC Futures Client - Optimized for Futures Trading Only

This is a streamlined version of the MEXC client that focuses exclusively
on futures trading operations. All spot trading functionality has been
removed for better performance and maintainability.

Author: Trading Bot System
Version: 2.0 (Futures Only)
"""

import aiohttp
import asyncio
import hashlib
import hmac
import json
import logging
import random
import time
from typing import Dict, Optional, List
import yaml

class MEXCFuturesClient:
    """
    Optimized MEXC Futures Trading Client
    
    Features:
    - Futures positions management
    - Futures account balance
    - Futures open orders
    - Price data retrieval
    - Streamlined API calls
    """
    
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        """Load trading configuration from config.ymal"""
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
        except Exception as e:
            logging.warning(f"Could not load MEXC config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _get_headers(self, signed: bool = False) -> Dict[str, str]:
        """Get request headers"""
        headers = {'Content-Type': 'application/json'}
        if signed and self.api_key:
            headers['ApiKey'] = self.api_key
        return headers

    async def _retry_api_call(self, func, *args, max_retries: int = 3, **kwargs):
        """Retry API call up to max_retries times if it fails or returns None"""
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                if result is not None:
                    return result
                logging.warning(f"API call returned None, attempt {attempt + 1}/{max_retries}")
            except Exception as e:
                logging.error(f"API call failed on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    raise

            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # Wait 1 second before retry

        return None

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        if not self.api_secret:
            return ""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _generate_futures_signature(self, sign_params: str = None) -> str:
        """Generate futures API signature - compatible with original format"""
        if not self.api_secret:
            return ""
        timestamp = int(time.time() * 1000)
        if sign_params:
            sign = f"{self.api_key}{timestamp}{sign_params}"
        else:
            sign = f"{self.api_key}{timestamp}"
        return hmac.new(
            self.api_secret.encode('utf-8'),
            sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _get_futures_headers(self) -> dict:
        """Get futures API headers"""
        timestamp = int(time.time() * 1000)
        return {
            "ApiKey": self.api_key,
            "Request-Time": str(timestamp),
            "Content-Type": "application/json"
        }

    async def _get_price_data_internal(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        """Internal method to get price data from MEXC with random delay"""
        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.base_url}/api/v3/ticker/24hr"
        params = {"symbol": symbol}

        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                return self._format_price_data(data, symbol)
            else:
                logging.error(f"Error getting price data for {symbol}: {response.status}")
                return None

    async def get_price_data(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        """Get price data from MEXC with retry mechanism"""
        return await self._retry_api_call(self._get_price_data_internal, symbol)

    def _format_price_data(self, ticker_data: Dict, symbol: str = "PAXGUSDT") -> Dict:
        """Format price data for display - removed CHG DAY and day open price fields"""
        def safe_float(value, default=0.0):
            try:
                return float(value) if value else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))
        price_change_24h = safe_float(ticker_data.get("priceChange"))
        price_change_percent_24h = safe_float(ticker_data.get("priceChangePercent"))

        return {
            "symbol": ticker_data.get("symbol", symbol),
            "current_price": current_price,
            "price_change_24h": price_change_24h,
            "price_change_percent_24h": price_change_percent_24h,
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume"))
        }

    async def _get_futures_positions_internal(self, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/position/open_positions"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_futures_signature(query_string if query_string else None)

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.debug(f"Futures positions API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures positions API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    positions_data = result.get('data', []) if result.get('success') else []
                    logging.debug(f"Futures positions API - Parsed data: {len(positions_data) if positions_data else 0} positions")
                    return positions_data
                except Exception as e:
                    logging.error(f"Error parsing futures positions response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures positions: {response.status}, Response: {response_text}")
                return None

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_positions_internal, symbol)
        return result if result is not None else []

    async def _get_futures_account_assets_internal(self) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/account/assets"
        signature = self._generate_futures_signature()

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.debug(f"Futures account assets API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures account assets API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    assets_data = result.get('data', []) if result.get('success') else []
                    logging.debug(f"Futures account assets API - Parsed data: {len(assets_data) if assets_data else 0} assets")
                    return assets_data
                except Exception as e:
                    logging.error(f"Error parsing futures account assets response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures account assets: {response.status}, Response: {response_text}")
                return None

    async def get_futures_account_assets(self) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_account_assets_internal)
        return result if result is not None else []

    async def _get_futures_open_orders_internal(self, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/order/list/open_orders"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_futures_signature(query_string if query_string else None)

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.debug(f"Futures open orders API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures open orders API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    orders_data = result.get('data', []) if result.get('success') else []
                    logging.debug(f"Futures open orders API - Parsed data: {len(orders_data) if orders_data else 0} orders")
                    return orders_data
                except Exception as e:
                    logging.error(f"Error parsing futures open orders response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures open orders: {response.status}, Response: {response_text}")
                return None

    async def get_futures_open_orders(self, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_open_orders_internal, symbol)
        return result if result is not None else []

# Alias for backward compatibility
MEXCClient = MEXCFuturesClient
