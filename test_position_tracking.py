#!/usr/bin/env python3
"""
Test script to monitor position tracking for 5 minutes
Check if positions disappear during updates
"""

import asyncio
import logging
import time
from datetime import datetime
from mexc_futures_client import MEXCFuturesClient

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class PositionTracker:
    def __init__(self):
        self.position_history = []
        self.update_count = 0
        
    async def track_positions(self, duration_minutes=5):
        """Track positions for specified duration"""
        end_time = time.time() + (duration_minutes * 60)
        
        print(f"🚀 Starting position tracking for {duration_minutes} minutes...")
        print(f"⏰ Will stop at: {datetime.fromtimestamp(end_time).strftime('%H:%M:%S')}")
        print("-" * 60)
        
        while time.time() < end_time:
            self.update_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            try:
                async with MEXCFuturesClient() as client:
                    # Get all trading data
                    positions = await client.get_futures_positions()
                    balance = await client.get_futures_account_assets()
                    orders = await client.get_futures_open_orders()
                    
                    # Process positions
                    active_positions = []
                    if positions:
                        for pos in positions:
                            hold_vol = float(pos.get('holdVol', 0))
                            if hold_vol > 0:
                                symbol = pos.get('symbol', 'N/A')
                                position_type = pos.get('positionType', 1)
                                side = 'LONG' if position_type == 1 else 'SHORT'
                                entry_price = float(pos.get('holdAvgPrice', 0))
                                position_id = pos.get('positionId', 0)
                                
                                # Convert volume for PAXG
                                if symbol == 'PAXG_USDT':
                                    size_asset = hold_vol / 1000.0
                                    asset_unit = 'PAXG'
                                else:
                                    size_asset = hold_vol * 0.001
                                    asset_unit = symbol.split('_')[0]
                                
                                # Get PnL
                                unrealized_pnl = 0.0
                                if balance:
                                    for asset in balance:
                                        if asset.get('currency') == 'USDT':
                                            unrealized_pnl = float(asset.get('unrealized', 0))
                                            break
                                
                                position_info = {
                                    'symbol': symbol,
                                    'side': side,
                                    'size': size_asset,
                                    'entry_price': entry_price,
                                    'pnl': unrealized_pnl,
                                    'position_id': position_id,
                                    'hold_vol': hold_vol
                                }
                                active_positions.append(position_info)
                    
                    # Store in history
                    snapshot = {
                        'timestamp': current_time,
                        'update_count': self.update_count,
                        'positions': active_positions,
                        'orders_count': len(orders) if orders else 0
                    }
                    self.position_history.append(snapshot)
                    
                    # Display current status
                    print(f"[{current_time}] Update #{self.update_count}")
                    if active_positions:
                        for pos in active_positions:
                            clean_symbol = pos['symbol'].replace('_USDT', '')
                            pnl_icon = "🔴" if pos['pnl'] < 0 else "🟢"
                            print(f"  {clean_symbol} {pos['side']} {pnl_icon} ${pos['pnl']:.4f}")
                            print(f"  Entry: ${pos['entry_price']:.2f} | Size: {pos['size']:.1f} {clean_symbol}")
                            print(f"  Position ID: {pos['position_id']} | Hold Vol: {pos['hold_vol']}")
                    else:
                        print("  📊 No open positions")
                    
                    print(f"  📋 Open Orders: {len(orders) if orders else 0}")
                    print("-" * 40)
                    
            except Exception as e:
                print(f"❌ Error in update #{self.update_count}: {e}")
                logging.error(f"Error in position tracking: {e}")
            
            # Wait 30 seconds between updates
            await asyncio.sleep(30)
        
        print(f"✅ Position tracking completed after {duration_minutes} minutes")
        self.analyze_results()
    
    def analyze_results(self):
        """Analyze tracking results for issues"""
        print("\n" + "="*60)
        print("📊 ANALYSIS RESULTS")
        print("="*60)
        
        if not self.position_history:
            print("❌ No data collected")
            return
        
        # Check for position disappearances
        position_counts = [len(snapshot['positions']) for snapshot in self.position_history]
        max_positions = max(position_counts) if position_counts else 0
        min_positions = min(position_counts) if position_counts else 0
        
        print(f"📈 Total updates: {len(self.position_history)}")
        print(f"📊 Position count range: {min_positions} - {max_positions}")
        
        # Look for sudden drops to zero
        zero_position_updates = [i for i, count in enumerate(position_counts) if count == 0]
        if zero_position_updates:
            print(f"⚠️  Found {len(zero_position_updates)} updates with zero positions:")
            for i in zero_position_updates:
                snapshot = self.position_history[i]
                print(f"   Update #{snapshot['update_count']} at {snapshot['timestamp']}")
        
        # Check for position ID consistency
        all_position_ids = set()
        for snapshot in self.position_history:
            for pos in snapshot['positions']:
                all_position_ids.add(pos['position_id'])
        
        if all_position_ids:
            print(f"🆔 Unique position IDs seen: {sorted(all_position_ids)}")
            
            # Check if same position ID appears and disappears
            for pos_id in all_position_ids:
                appearances = []
                for i, snapshot in enumerate(self.position_history):
                    has_position = any(pos['position_id'] == pos_id for pos in snapshot['positions'])
                    appearances.append(has_position)
                
                # Look for gaps (True, False, True pattern)
                gaps = []
                for i in range(1, len(appearances) - 1):
                    if appearances[i-1] and not appearances[i] and appearances[i+1]:
                        gaps.append(i)
                
                if gaps:
                    print(f"⚠️  Position ID {pos_id} disappeared temporarily in updates: {gaps}")
        
        print("\n📋 Last 3 snapshots:")
        for snapshot in self.position_history[-3:]:
            print(f"  [{snapshot['timestamp']}] Positions: {len(snapshot['positions'])}, Orders: {snapshot['orders_count']}")

async def main():
    tracker = PositionTracker()
    await tracker.track_positions(duration_minutes=5)

if __name__ == "__main__":
    asyncio.run(main())
