import aiohttp
import asyncio
import json
import logging
import hmac
import hashlib
import time
from typing import Dict, Optional, List
import yaml

class MEXCClient:
    """
    MEXC Futures Trading Client - Optimized for Futures Only

    This client is specifically designed for MEXC Futures trading operations.
    All Spot trading functionality has been removed for better performance.
    """

    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_trading_config()

    def load_trading_config(self):
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
        except Exception as e:
            logging.warning(f"Could not load MEXC trading config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _generate_signature(self, query_string: str) -> str:
        if not self.api_secret:
            return ""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _generate_futures_signature(self, sign_params: str = None) -> str:
        if not self.api_secret:
            return ""
        timestamp = int(time.time() * 1000)
        if sign_params:
            sign = f"{self.api_key}{timestamp}{sign_params}"
        else:
            sign = f"{self.api_key}{timestamp}"
        return hmac.new(
            self.api_secret.encode('utf-8'),
            sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _get_futures_headers(self) -> dict:
        timestamp = int(time.time() * 1000)
        return {
            "ApiKey": self.api_key,
            "Request-Time": str(timestamp),
            "Content-Type": "application/json"
        }

    def _get_headers(self, signed: bool = False) -> Dict:
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MEXC-Bot/1.0'
        }
        if signed and self.api_key:
            headers['X-MEXC-APIKEY'] = self.api_key
        return headers

    async def _retry_api_call(self, func, *args, max_retries: int = 3, **kwargs):
        """Retry API call up to max_retries times if it fails or returns None"""
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                if result is not None:
                    return result
                logging.warning(f"API call returned None, attempt {attempt + 1}/{max_retries}")
            except Exception as e:
                logging.error(f"API call failed on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    raise

            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # Wait 1 second before retry

        return None
    
    async def _get_ticker_24hr_internal(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        url = f"{self.base_url}/api/v3/ticker/24hr"
        params = {"symbol": symbol}

        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                return data
            else:
                logging.error(f"Error getting ticker: {response.status}")
                return None

    async def get_ticker_24hr(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        return await self._retry_api_call(self._get_ticker_24hr_internal, symbol)
    
    async def _get_klines_internal(self, symbol: str = "PAXGUSDT", interval: str = "1d", limit: int = 1) -> Optional[list]:
        url = f"{self.base_url}/api/v3/klines"
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }

        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                return data
            else:
                logging.error(f"Error getting klines: {response.status}")
                return None

    async def get_klines(self, symbol: str = "PAXGUSDT", interval: str = "1d", limit: int = 1) -> Optional[list]:
        return await self._retry_api_call(self._get_klines_internal, symbol, interval, limit)
    
    async def get_price_data(self) -> Optional[Dict]:
        ticker_data = await self.get_ticker_24hr()

        if not ticker_data:
            return None

        def safe_float(value, default=0.0):
            try:
                return float(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))
        price_change_24h = safe_float(ticker_data.get("priceChange"))
        price_change_percent_24h = safe_float(ticker_data.get("priceChangePercent"))

        result = {
            "symbol": ticker_data.get("symbol", "PAXGUSDT"),
            "current_price": current_price,
            "price_change_24h": price_change_24h,
            "price_change_percent_24h": price_change_percent_24h,
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume"))
        }

        return result




    async def _get_futures_positions_internal(self, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        url = "https://contract.mexc.com/api/v1/private/position/open_positions"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_futures_signature(query_string if query_string else None)

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get('success') and data.get('data'):
                    return data['data']
                return []
            else:
                logging.error(f"Error getting futures positions: {response.status}")
                text = await response.text()
                logging.error(f"Response: {text}")
                return None

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_positions_internal, symbol)
        return result if result is not None else []

    async def _get_futures_balance_internal(self) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            return None

        url = "https://contract.mexc.com/api/v1/private/account/assets"
        signature = self._generate_futures_signature()

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        async with self.session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get('success'):
                    return data.get('data', {})
                return None
            else:
                logging.error(f"Error getting futures balance: {response.status}")
                text = await response.text()
                logging.error(f"Response: {text}")
                return None

    async def get_futures_balance(self) -> Optional[Dict]:
        return await self._retry_api_call(self._get_futures_balance_internal)

    async def get_futures_account_assets(self) -> Optional[Dict]:
        """Get futures account assets - same as get_futures_balance but with clearer name"""
        return await self.get_futures_balance()

    async def _get_futures_account_asset_currency_internal(self, currency: str) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            return None

        url = f"https://contract.mexc.com/api/v1/private/account/asset/{currency}"

        signature = self._generate_futures_signature()
        headers = self._get_futures_headers()
        headers["Signature"] = signature

        async with self.session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get('success'):
                    return data.get('data', {})
                return None
            else:
                logging.error(f"Error getting futures asset {currency}: {response.status}")
                text = await response.text()
                logging.error(f"Response: {text}")
                return None

    async def get_futures_account_asset_currency(self, currency: str) -> Optional[Dict]:
        return await self._retry_api_call(self._get_futures_account_asset_currency_internal, currency)

    async def _get_futures_history_positions_internal(self, page_num: int, page_size: int = None, symbol: str = None) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            return None

        url = "https://contract.mexc.com/api/v1/private/position/list/history_positions"

        data_original = {'page_num': page_num}
        if page_size:
            data_original['page_size'] = page_size
        if symbol:
            data_original['symbol'] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_futures_signature(query_string)

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get('success'):
                    return data.get('data', {})
                return None
            else:
                logging.error(f"Error getting futures history positions: {response.status}")
                text = await response.text()
                logging.error(f"Response: {text}")
                return None

    async def get_futures_history_positions(self, page_num: int, page_size: int = None, symbol: str = None) -> Optional[Dict]:
        return await self._retry_api_call(self._get_futures_history_positions_internal, page_num, page_size, symbol)

    async def _get_futures_open_orders_internal(self, page_num: int = None, page_size: int = None, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        url = "https://contract.mexc.com/api/v1/private/order/list/open_orders"

        data_original = {}
        if page_num:
            data_original['page_num'] = page_num
        if page_size:
            data_original['page_size'] = page_size
        if symbol:
            data_original['symbol'] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_futures_signature(query_string if query_string else None)

        headers = self._get_futures_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                if data.get('success') and data.get('data'):
                    return data['data']
                return []
            else:
                logging.error(f"Error getting futures open orders: {response.status}")
                text = await response.text()
                logging.error(f"Response: {text}")
                return None

    async def get_futures_open_orders(self, page_num: int = None, page_size: int = None, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_open_orders_internal, page_num, page_size, symbol)
        return result if result is not None else []

    async def _get_multiple_tickers_internal(self, symbols: List[str]) -> Optional[List[Dict]]:
        url = f"{self.base_url}/api/v3/ticker/24hr"

        results = []
        for symbol in symbols:
            params = {"symbol": symbol}
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results.append(data)
                else:
                    logging.error(f"Error getting ticker for {symbol}: {response.status}")

        return results if results else None

    async def get_multiple_tickers(self, symbols: List[str]) -> Optional[List[Dict]]:
        return await self._retry_api_call(self._get_multiple_tickers_internal, symbols)

    async def _get_watchlist_data_internal(self) -> Optional[Dict]:
        symbols = [
            "BTCUSDT", "PAXGUSDT", "ETHUSDT", "XRPUSDT",
            "SOLUSDT", "DOGEUSDT", "ENAUSDT", "LINKUSDT",
            "BNBUSDT", "NEARUSDT", "ADAUSDT", "AVAXUSDT"
        ]

        tickers = await self.get_multiple_tickers(symbols)
        if not tickers:
            return None

        def safe_float(value, default=0.0):
            try:
                return float(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        watchlist = []
        for ticker in tickers:
            symbol = ticker.get("symbol", "")
            token_name = symbol.replace("USDT", "")

            current_price = safe_float(ticker.get("lastPrice"))
            price_change_24h = safe_float(ticker.get("priceChange"))
            price_change_percent_24h = safe_float(ticker.get("priceChangePercent"))
            volume = safe_float(ticker.get("volume"))

            if volume >= 1000000000:
                volume_str = f"{volume/1000000000:.1f}B"
            elif volume >= 1000000:
                volume_str = f"{volume/1000000:.1f}M"
            elif volume >= 1000:
                volume_str = f"{volume/1000:.1f}K"
            else:
                volume_str = f"{volume:.1f}"

            watchlist.append({
                "token": token_name,
                "symbol": symbol,
                "price": current_price,
                "change_24h": price_change_24h,
                "change_percent_24h": price_change_percent_24h,
                "volume": volume,
                "volume_str": volume_str
            })

        return {
            "watchlist": watchlist,
            "timestamp": int(time.time())
        }

    async def get_watchlist_data(self) -> Optional[Dict]:
        return await self._retry_api_call(self._get_watchlist_data_internal)
